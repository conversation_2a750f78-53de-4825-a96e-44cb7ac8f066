import { Brand as MultiLocationBrand } from '@vendasta/multi-location';

export class Brand {
  //Identifiers
  name: string;
  path: string;
  private _groupNodes: string[]; // The path to this brand/region in groups
  marketId: string;

  //Info
  activatedSources: number[];

  //TabStatuses
  socialTabVisible: boolean;
  reviewsTabVisible: boolean;
  listingsTabVisible: boolean;
  googleQAndATabVisible: boolean;
  advertisingTabVisible: boolean;
  dataExportTabVisible: boolean;
  websiteTabVisible: boolean;
  reportTabVisible: boolean;
  keywordTrackingTabVisible: boolean;
  analyticsTabVisible: boolean;

  //FeatureStatuses
  customerVoiceExecutiveReportVisible: boolean;

  constructor(brand: MultiLocationBrand) {
    //Identifiers
    this._groupNodes = brand.group.path.nodes;
    this.name = brand.group.name;
    this.path = brand.brandPath;
    this.marketId = brand.marketId;
    //Info
    this.activatedSources = brand.activatedVisibilitySources.map((x) => parseInt(x));
    //TabStatuses
    this.socialTabVisible = brand.tabStatuses.socialTabEnabled;
    this.reviewsTabVisible = brand.tabStatuses.reviewsTabEnabled;
    this.listingsTabVisible = brand.tabStatuses.listingsTabEnabled;
    this.advertisingTabVisible = brand.tabStatuses.advertisingTabEnabled;
    this.websiteTabVisible = brand.tabStatuses.websiteTabEnabled;
    this.googleQAndATabVisible = brand.tabStatuses.googleQAndATabEnabled;
    this.dataExportTabVisible = brand.tabStatuses.dataExportTabEnabled;
    this.reportTabVisible = brand.tabStatuses.reportTabEnabled;
    this.keywordTrackingTabVisible = brand.tabStatuses['keywordTrackingTabEnabled'] ?? brand.tabStatuses.listingsTabEnabled;
    this.analyticsTabVisible = brand.tabStatuses['analyticsTabEnabled'] ?? brand.tabStatuses.listingsTabEnabled;
    //FeatureStatuses
    this.customerVoiceExecutiveReportVisible = brand.featureStatuses.customerVoiceExecutiveReportEnabled;
  }

  get groupNodes(): string[] {
    return Object.assign([], this._groupNodes);
  }

  get title(): string {
    return this.name;
  }

  get subtitle(): string {
    return 'Brand';
  }
}
