import { HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, timer } from 'rxjs';
import { delayWhen, map, retry, take, tap } from 'rxjs/operators';
import { ApiService } from '../api-service/api.service';

import { Node } from './node';

import { FeatureStatusesInterface, TabStatusesInterface } from '@vendasta/multi-location';
import { User } from '../core/user';

export interface Source {
  name: string;
  sourceId: number;
  sourceTypeId: string;
  sourceTypeName: string;
  iconUrl16px: string;
  iconUrl32px: string;
  iconUrl50px: string;
  disabledForBrand: boolean;
  descriptionHtml: string;
  taxonomyWhitelist: string[];
  locationWhitelist: string[];
  isLspSource: boolean;
  isLspYextSource: boolean;
}

interface AllUsersResponse {
  paths: Record<string, string[]>;
  users: User[];
}

export interface BrandUser {
  brandPaths: string[];
  firstName: string;
  lastName: string;
  name: string;
  email: string;
  userId: string;
  accessToAll: boolean;
}

export interface BrandAPIResponse {
  successful: boolean;
  message?: string;
}

const URLS = {
  BULK_ACTIONS: {
    REFRESH_SNAPSHOT: '/_ajax/v1/action-lists/refresh-snapshot/',
    EXPORT_NAP_DATA: '/_ajax/v1/action-lists/export-nap-product/',
  },
};

@Injectable({ providedIn: 'root' })
export class BrandsService {
  private readonly activatedSources$$: BehaviorSubject<number[]> = new BehaviorSubject(undefined);
  readonly activatedSources$: Observable<number[]> = this.activatedSources$$.asObservable();
  private readonly sources$$: BehaviorSubject<Source[]> = new BehaviorSubject(undefined);
  readonly sources$: Observable<Source[]> = this.sources$$.asObservable();
  private readonly brandUsers$$: BehaviorSubject<BrandUser[]> = new BehaviorSubject<BrandUser[]>(undefined);
  readonly brandUsers$: Observable<BrandUser[]> = this.brandUsers$$.asObservable();

  constructor(private apiService: ApiService) {}

  loadBrands(): Observable<Node[]> {
    const url = '/_ajax/v1/brands/names/?detailsFlag=true';
    return this.apiService.get(url).pipe(
      map((resp) =>
        resp.details.map((node) => {
          return {
            name: node.brandName,
            isLocation: false,
            path: node.path,
          };
        }),
      ),
    );
  }

  loadTree(path?: string): Observable<Node[]> {
    const args = path ? 'path=' + encodeURIComponent(path) : '';
    const url = `/brand-analytics/get-tree/`;
    return this.apiService
      .post(url, args, { headers: new HttpHeaders({ 'Content-Type': 'application/x-www-form-urlencoded' }) })
      .pipe(
        map((resp) =>
          resp.tree.children.map((node) => {
            return {
              name: node.name,
              isLocation: node.isLocation,
              path: decodeURIComponent(node.path),
            };
          }),
        ),
      );
  }

  fetchActivatedSources(brand: string): void {
    const url = `/brand-analytics/get-visibility-sources/`;
    const args = brand ? 'brand=' + encodeURIComponent(brand) : '';
    this.apiService
      .post(url, args, { headers: new HttpHeaders({ 'Content-Type': 'application/x-www-form-urlencoded' }) })
      .pipe(map((resp) => resp.activatedSources))
      .subscribe((resp: any) => {
        this.activatedSources$$.next(resp);
      });
  }

  fetchSources(partnerId: string, marketId?: string): void {
    let url = `/api/v3/brand-analytics/get-sources/?partnerId=${partnerId}`;
    if (marketId) {
      url += '&marketId=' + encodeURIComponent(marketId);
    }
    this.apiService
      .post(url, '', { headers: new HttpHeaders({ 'Content-Type': 'application/x-www-form-urlencoded' }) })
      .pipe(map((resp) => resp.sources))
      .subscribe((resp: any) => {
        this.sources$$.next(resp);
      });
  }

  updateSingleActivatedSource(brand: string, sourceId: number, enablingSource: boolean): Observable<boolean> {
    const activatedSources = this.activatedSources$$.getValue();
    const indexOfSource = activatedSources.indexOf(sourceId);
    if (enablingSource && indexOfSource < 0) {
      activatedSources.push(sourceId);
    } else if (!enablingSource && indexOfSource >= 0) {
      activatedSources.splice(indexOfSource, 1);
    }
    this.activatedSources$$.next(activatedSources);
    return this.setActivatedSources(brand, activatedSources).pipe(map((resp) => resp.status === 'success'));
  }

  private setActivatedSources(brand: string, activatedSourcesList: number[]): Observable<{ status: string }> {
    const url = `/brand-analytics/set-visibility-sources/`;
    let args = '';
    if (brand) {
      args += 'brand=' + encodeURIComponent(brand) + '&';
    }
    if (activatedSourcesList) {
      activatedSourcesList.forEach((element) => {
        args += 'activatedSources=' + element + '&';
      });
    }
    return this.apiService.post(url, args, {
      headers: new HttpHeaders({ 'Content-Type': 'application/x-www-form-urlencoded' }),
    });
  }

  fetchBrandUsers(partnerId: string, brandPath: string): void {
    const url = `/_ajax/v1/brand/users/all?partnerId=${partnerId}&brandPath=${encodeURIComponent(
      partnerId + '|' + brandPath,
    )}`;
    this.apiService
      .get(url)
      .pipe(map((res) => this.convertAllUsersResponse(res, `${partnerId + '|' + brandPath}`)))
      .subscribe({
        next: (users) => {
          this.brandUsers$$.next(users);
        },
      });
  }

  private convertAllUsersResponse(response: AllUsersResponse, brandPath: string): BrandUser[] {
    return (
      response.users
        .map((user) => {
          const brandPaths: string[] = [];
          let accessToAll = false;
          Object.keys(response.paths).forEach((key) => {
            // Have to check the second half for if the brand path is there because brands could have the same starting name
            // such as:
            //    VUNI|all
            //    VUNI|all2
            //    VUNI|all3
            if (
              response.paths[key].indexOf(user.userId) > -1 &&
              (key.startsWith(`${brandPath}|`) || key === brandPath)
            ) {
              brandPaths.push(key.replace(/\|/gi, ' / '));

              // If they have a key the exactly matches the brandpath, that means they have access to the full brand
              if (key === brandPath) {
                accessToAll = true;
              }
            }
          });
          return {
            brandPaths: this.orderBrandPathsAlphabetically(brandPaths),
            firstName: user.firstName,
            lastName: user.lastName,
            name: `${user.firstName || ''} ${user.lastName || ''}`.trim(),
            email: user.email,
            userId: user.userId,
            manageUserLink: `/bc-admin/users/${user.userId}/permissions`,
            accessToAll,
          };
        })
        // Remove any user that has 0 brand paths
        .filter((user) => user.brandPaths.length !== 0)
    );
  }

  private orderBrandPathsAlphabetically(brandPaths: string[]): string[] {
    return brandPaths.sort((a, b) => {
      if (a < b) {
        return -1;
      }
      if (a > b) {
        return 1;
      }
      return 0;
    });
  }

  removeUserFromBrandPath(userId: string, brandPath: string): Observable<void> {
    // Have to convert the path back to pipes, as for display we use slashes
    const convertedBrandPath = brandPath.replace(/ \/ /gi, '|');
    const associationId = `${userId}-${convertedBrandPath}-NB`;
    const url = `/bc-admin/user/${userId}/permissions/delete/`;
    return this.apiService
      .post(url, 'association_key=' + encodeURIComponent(associationId), {
        headers: new HttpHeaders({ 'Content-Type': 'application/x-www-form-urlencoded' }),
      })
      .pipe(
        tap(() => {
          this.brandUsers$$.next(
            this.brandUsers$$.getValue().map((user) => {
              if (user.userId === userId) {
                user.brandPaths = user.brandPaths.filter((path) => path !== brandPath);
              }
              return user;
            }),
          );
        }),
      );
  }

  updateBrandMarket(brandName: string, marketId: string): Observable<any> {
    const encodedBrandMarket = encodeURIComponent(brandName);
    return this.apiService.post(
      `/_ajax/v1/brand/update-market?marketId=${marketId}&brandName=${encodedBrandMarket}`,
      {},
    );
  }

  removeUserFromMultipleBrandPaths(userId: string, brandPaths: string[]): Observable<void> {
    const convertedBrandPath = brandPaths.map((path) => `${userId}-${path.replace(/ \/ /gi, '|').toUpperCase()}-NB`);
    return this.apiService
      .post('/_ajax/v1/user-association/delete/multiple/', {
        userId,
        associationKeys: convertedBrandPath,
      })
      .pipe(
        tap(() => {
          this.brandUsers$$.next(this.brandUsers$$.getValue().filter((user) => user.userId !== userId));
        }),
      );
  }

  updateFeatureList(
    partnerId: string,
    brandName: string,
    tabStatus: TabStatusesInterface,
    featureStatus: FeatureStatusesInterface,
  ): Observable<unknown> {
    return this.apiService
      .post('/api/v3/brand-analytics/update-features/', {
        partnerId,
        brandName,
        visibilityTabEnabledFlag: tabStatus.listingsTabEnabled,
        reviewsTabEnabledFlag: tabStatus.reviewsTabEnabled,
        socialTabEnabledFlag: tabStatus.socialTabEnabled,
        mapTabEnabledFlag: tabStatus.mapTabEnabled,
        advertisingTabEnabledFlag: tabStatus.advertisingTabEnabled,
        reportTabEnabledFlag: tabStatus.reportTabEnabled,
        dataExportTabEnabledFlag: tabStatus.dataExportTabEnabled,
        websiteTabEnabledFlag: tabStatus.websiteTabEnabled,
        customerVoiceExecutiveReportEnabledFlag: featureStatus.customerVoiceExecutiveReportEnabled,
        googleQAndATabEnabledFlag: tabStatus.googleQAndATabEnabled,
        keywordTrackingTabEnabledFlag: tabStatus['keywordTrackingTabEnabled'],
        analyticsTabEnabledFlag: tabStatus['analyticsTabEnabled'],
      })
      .pipe(
        retry({
          delay: (errors) =>
            errors.pipe(
              delayWhen(() => timer(2000)),
              take(3),
            ),
        }),
      );
  }

  addNodeToGroup(urlSafePath: string, accountGroupId?: string): Observable<BrandAPIResponse> {
    const url = `/brand-analytics/create-node/`;

    let body = `path=${urlSafePath}`;
    if (accountGroupId) {
      body += `&accountGroupId=${accountGroupId}`;
    }

    return this.apiService
      .post(url, body, {
        headers: new HttpHeaders({ 'Content-Type': 'application/x-www-form-urlencoded' }),
      })
      .pipe(
        map((resp) => {
          // Location and any missing parent regions were successfully added
          if (resp && resp.created && resp.created > 0) {
            return {
              successful: true,
            };
          }
          return {
            successful: false,
            message:
              resp && resp.message
                ? this.convertAddNodeErrorMessage(resp.message, !!accountGroupId)
                : 'Something went wrong. Please try again.',
          };
        }),
      );
  }

  private convertAddNodeErrorMessage(errorMessage: string, nodeIsLocation: boolean): string {
    if (nodeIsLocation) {
      if (errorMessage === 'This node already exists.') {
        return 'An account with this name and address already exists in this sub-group. Check for duplicate information.';
      } else if (errorMessage?.includes('has sub regions and cannot have a location at this level')) {
        return 'Cannot add account to the group or sub-group that contains sub-groups at the same level.';
      } else if (errorMessage?.includes('has locations and cannot have a sub region at this level')) {
        return 'Cannot add account to the group or sub-group whose parent sub-group has accounts at the same level.';
      }
    } else {
      if (errorMessage === 'This node already exists.') {
        return 'A sub-group with this name already exists in this group. Check for duplicate information.';
      } else if (errorMessage?.includes('has locations and cannot have a sub region at this level')) {
        return 'Cannot add sub-group to a group or sub-group that contains locations at the same level.';
      }
    }
    return 'Something went wrong. Please try again.';
  }

  deleteLocationFromPath(accountGroupId: string, path: string): Observable<boolean> {
    const url = `/brand-analytics/delete-location/`;

    return this.apiService
      .post(url, `accountGroupId=${accountGroupId}&path=${encodeURIComponent(path)}`, {
        headers: new HttpHeaders({ 'Content-Type': 'application/x-www-form-urlencoded' }),
      })
      .pipe(
        map((resp) => {
          return resp && resp.deleted && resp.deleted === 1;
        }),
      );
  }

  deleteSubGroup(path: string): Observable<boolean> {
    const url = `/brand-analytics/delete-node/`;

    return this.apiService
      .post(url, `path=${encodeURIComponent(path)}`, {
        headers: new HttpHeaders({ 'Content-Type': 'application/x-www-form-urlencoded' }),
      })
      .pipe(
        map((resp) => {
          return resp && resp.deleted && resp.deleted >= 1;
        }),
      );
  }

  createBrand(
    brandName: string,
    partnerId: string,
    marketId: string,
    location: string,
    taxonomyId: string,
  ): Observable<string> {
    return this.apiService
      .post('/api/v3/brand-analytics/create-brand/', {
        partnerId: partnerId,
        marketId: marketId,
        brandName: brandName,
        location: location,
        taxonomyId: taxonomyId,
      })
      .pipe(map(() => brandName));
  }

  getLegacyReportUrlForBrand(brandPath: string): Observable<string> {
    const url = `/api/v3/brand-analytics/get-legacy-report-url/`;
    const args = 'brandPath=' + encodeURIComponent(brandPath);
    return this.apiService
      .post(url, args, {
        headers: new HttpHeaders({ 'Content-Type': 'application/x-www-form-urlencoded' }),
      })
      .pipe(map((resp) => resp.legacyReportUrl));
  }

  createOrRefreshSnapshotsForBrand(groupId: string): Observable<any> {
    const body = {
      groupId,
    };
    return this.apiService.post(URLS.BULK_ACTIONS.REFRESH_SNAPSHOT, body);
  }

  exportAccountData(groupId: string): Observable<any> {
    const body = {
      groupId,
    };
    return this.apiService.post(URLS.BULK_ACTIONS.EXPORT_NAP_DATA, body);
  }
}
